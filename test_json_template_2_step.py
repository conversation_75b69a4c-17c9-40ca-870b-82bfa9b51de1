"""
Test script để kiểm tra việc tách tạo nội dung thành 2 b<PERSON><PERSON><PERSON> call LLM
trong JsonTemplateService
"""

import asyncio
import logging
from app.services.json_template_service import get_json_template_service

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_2_step_content_generation():
    """Test việc tách tạo nội dung thành 2 bước"""
    
    # Sample lesson content
    lesson_content = """
    Nguyên tố hóa học là tập hợp các nguyên tử có cùng số proton trong hạt nhân. 
    Mỗi nguyên tố được xác định bởi số hiệu nguyên tử (Z).
    
    Cấu trúc nguyên tử gồm:
    - Hạt nhân: chứa proton và neutron
    - Electron: chuyển động xung quanh hạt nhân
    
    Bảng tuần hoàn sắp xếp các nguyên tố theo số hiệu nguyên tử tăng dần.
    """
    
    # Sample template JSON
    template_json = {
        "version": "1.0",
        "slideFormat": "16:9",
        "slides": [
            {
                "id": "slide_001",
                "elements": [
                    {
                        "id": "title_001",
                        "text": "LessonName 100",
                        "type": "text"
                    },
                    {
                        "id": "desc_001", 
                        "text": "LessonDescription 200",
                        "type": "text"
                    },
                    {
                        "id": "date_001",
                        "text": "CreatedDate 50",
                        "type": "text"
                    }
                ]
            },
            {
                "id": "slide_002",
                "elements": [
                    {
                        "id": "title_002",
                        "text": "TitleName 80",
                        "type": "text"
                    },
                    {
                        "id": "content_002",
                        "text": "TitleContent 300",
                        "type": "text"
                    }
                ]
            }
        ]
    }
    
    try:
        # Get service instance
        service = get_json_template_service()
        
        if not service.is_available():
            logger.error("❌ JsonTemplateService is not available")
            return
            
        logger.info("🚀 Testing 2-step content generation...")
        
        # Test the full process
        result = await service.process_json_template(
            lesson_id="test_lesson_001",
            template_json=template_json,
            config_prompt="Tạo nội dung chi tiết và dễ hiểu cho học sinh"
        )
        
        if result.get("success"):
            logger.info("✅ 2-step content generation successful!")
            logger.info(f"📊 Slides created: {result.get('slides_created', 0)}")
            
            # Log processed template structure
            processed_template = result.get("processed_template", {})
            slides = processed_template.get("slides", [])
            
            for i, slide in enumerate(slides):
                logger.info(f"📄 Slide {i+1}: {slide.get('id')} - {len(slide.get('elements', []))} elements")
                
        else:
            logger.error(f"❌ 2-step content generation failed: {result.get('error')}")
            
    except Exception as e:
        logger.error(f"❌ Test failed with exception: {e}")

if __name__ == "__main__":
    asyncio.run(test_2_step_content_generation())
